# flink-job.yaml
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: kafka-consumer-job
  namespace: flink
spec:
  image: flink:1.20-scala_2.12
  flinkVersion: v1_20
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    state.backend: filesystem
    execution.checkpointing.savepoint-dir: file:///flink-data/savepoints
    execution.checkpointing.dir: file:///flink-data/checkpoints
    execution.checkpointing.interval: 60s
    # high-availability: org.apache.flink.kubernetes.highavailability.KubernetesHaServicesFactory
    high-availability.type: kubernetes
    high-availability.kubernetes.cluster-id: kafka-consumer-job       
    high-availability.storageDir: file:///flink-data/ha
  serviceAccount: flink
  jobManager:
    resource:
      memory: "1024m"
      cpu: 1
  taskManager:
    resource:
      memory: "1024m"
      cpu: 1
  podTemplate:
    spec:
      containers:
        - name: flink-main-container
          volumeMounts:
            - mountPath: /flink-data
              name: flink-volume
          env:
            - name: KAFKA_BOOTSTRAP_SERVERS
              value: "test-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092"
          volumeMounts:
            - name: flink-config-volume
              mountPath: /opt/flink/lib/flink-sql-connector-kafka-3.3.0-1.20.jar
              subPath: flink-sql-connector-kafka-3.3.0-1.20.jar
      volumes:
        - name: flink-volume
          emptyDir: {}
        - name: flink-config-volume
          configMap:
            name: kafka-connector-config
  job:
    jarURI: local:///opt/flink/examples/streaming/WordCount.jar
    parallelism: 2
    upgradeMode: savepoint
