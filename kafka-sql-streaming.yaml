---
# ConfigMap with Flink SQL statements for Kafka streaming
apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-sql-scripts
  namespace: flink
data:
  kafka-streaming.sql: |
    -- Create source table reading from input-topic
    CREATE TABLE kafka_source (
        message STRING,
        event_time TIMESTAMP(3) METADATA FROM 'timestamp'
    ) WITH (
        'connector' = 'kafka',
        'topic' = 'input-topic',
        'properties.bootstrap.servers' = 'test-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092',
        'properties.group.id' = 'flink-consumer-group',
        'format' = 'raw'
    );
    
    -- Create sink table writing to output-topic
    CREATE TABLE kafka_sink (
        message STRING,
        processed_time TIMESTAMP(3)
    ) WITH (
        'connector' = 'kafka',
        'topic' = 'output-topic',
        'properties.bootstrap.servers' = 'test-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092',
        'format' = 'json'
    );
    
    -- Stream data from input-topic to output-topic with processing
    INSERT INTO kafka_sink
    SELECT 
        CONCAT('Processed: ', message) as message,
        CURRENT_TIMESTAMP as processed_time
    FROM kafka_source;

---
# Flink Session Cluster
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: kafka-streaming-cluster
  namespace: flink
spec:
  image: apache/flink:1.20.0-scala_2.12-java11
  flinkVersion: v1_20
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    state.backend: filesystem
    execution.checkpointing.dir: file:///flink-data/checkpoints
    execution.checkpointing.savepoint-dir: file:///flink-data/savepoints
    execution.checkpointing.interval: 60s
    high-availability.type: kubernetes
    high-availability.storageDir: file:///flink-data/ha
  serviceAccount: flink
  jobManager:
    resource:
      memory: "1024m"
      cpu: 1
  taskManager:
    resource:
      memory: "1024m"
      cpu: 1
  podTemplate:
    spec:
      initContainers:
        - name: setup-kafka-connector
          image: busybox:1.35
          command:
            - sh
            - -c
            - |
              echo "Downloading Kafka connector..."
              wget -O /flink-lib/flink-sql-connector-kafka-3.3.0-1.20.jar \
                https://repo1.maven.org/maven2/org/apache/flink/flink-sql-connector-kafka/3.3.0-1.20/flink-sql-connector-kafka-3.3.0-1.20.jar
              echo "Connector downloaded"
              ls -la /flink-lib/
          volumeMounts:
            - name: flink-lib
              mountPath: /flink-lib
      containers:
        - name: flink-main-container
          volumeMounts:
            - mountPath: /flink-data
              name: flink-volume
            - mountPath: /opt/flink/lib
              name: flink-lib
            - mountPath: /opt/flink/sql
              name: sql-scripts
          env:
            - name: KAFKA_BOOTSTRAP_SERVERS
              value: "test-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092"
      volumes:
        - name: flink-volume
          emptyDir: {}
        - name: flink-lib
          emptyDir: {}
        - name: sql-scripts
          configMap:
            name: flink-sql-scripts

---
# FlinkSessionJob to execute the SQL
apiVersion: flink.apache.org/v1beta1
kind: FlinkSessionJob
metadata:
  name: kafka-streaming-job
  namespace: flink
spec:
  deploymentName: kafka-streaming-cluster
  job:
    jarURI: local:///opt/flink/examples/table/StreamSQLExample.jar
    parallelism: 2
    upgradeMode: savepoint
    args:
      - "--sql-file"
      - "/opt/flink/sql/kafka-streaming.sql"