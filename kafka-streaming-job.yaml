# Flink Kafka Streaming Job
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: kafka-streaming-job
  namespace: flink
spec:
  image: apache/flink:1.20.0-scala_2.12-java11
  flinkVersion: v1_20
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    state.backend: filesystem
    execution.checkpointing.dir: file:///flink-data/checkpoints
    execution.checkpointing.savepoint-dir: file:///flink-data/savepoints
    execution.checkpointing.interval: 60s
    high-availability.type: kubernetes
    high-availability.cluster-id: kafka-streaming-job
    high-availability.storageDir: file:///flink-data/ha
  serviceAccount: flink
  jobManager:
    resource:
      memory: "1024m"
      cpu: 1
  taskManager:
    resource:
      memory: "1024m"
      cpu: 1
  podTemplate:
    spec:
      initContainers:
        - name: download-kafka-connector
          image: busybox:1.35
          command:
            - sh
            - -c
            - |
              echo "Downloading Kafka connector..."
              wget -O /flink-lib/flink-sql-connector-kafka-3.3.0-1.20.jar \
                https://repo1.maven.org/maven2/org/apache/flink/flink-sql-connector-kafka/3.3.0-1.20/flink-sql-connector-kafka-3.3.0-1.20.jar || \
              curl -L -o /flink-lib/flink-sql-connector-kafka-3.3.0-1.20.jar \
                https://repo1.maven.org/maven2/org/apache/flink/flink-sql-connector-kafka/3.3.0-1.20/flink-sql-connector-kafka-3.3.0-1.20.jar
              echo "Kafka connector downloaded successfully"
              ls -la /flink-lib/
          volumeMounts:
            - name: flink-lib
              mountPath: /flink-lib
      containers:
        - name: flink-main-container
          volumeMounts:
            - mountPath: /flink-data
              name: flink-volume
            - mountPath: /opt/flink/lib
              name: flink-lib
          env:
            - name: KAFKA_BOOTSTRAP_SERVERS
              value: "test-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092"
            - name: KAFKA_INPUT_TOPIC
              value: "input-topic"
            - name: KAFKA_OUTPUT_TOPIC
              value: "output-topic"
      volumes:
        - name: flink-volume
          emptyDir: {}
        - name: flink-lib
          emptyDir: {}
  job:
    # We'll create a custom job that streams from input-topic to output-topic
    entryClass: "org.apache.flink.streaming.examples.kafka.KafkaStreamingJob"
    jarURI: "https://repo1.maven.org/maven2/org/apache/flink/flink-examples-streaming_2.12/1.20.0/flink-examples-streaming_2.12-1.20.0.jar"
    parallelism: 2
    upgradeMode: savepoint
    args:
      - "--bootstrap.servers"
      - "test-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092"
      - "--input-topic"
      - "input-topic"
      - "--output-topic"
      - "output-topic"
      - "--group.id"
      - "flink-consumer-group"