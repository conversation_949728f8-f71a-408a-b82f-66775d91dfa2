# kafka-topics.yaml
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: input-topic
  namespace: kafka
  labels:
    strimzi.io/cluster: test-cluster
spec:
  partitions: 4
  replicas: 1
  config:
    retention.ms: 604800000
    segment.ms: 86400000
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: output-topic
  namespace: kafka
  labels:
    strimzi.io/cluster: test-cluster
spec:
  partitions: 4
  replicas: 1
  config:
    retention.ms: 604800000
    segment.ms: 86400000
