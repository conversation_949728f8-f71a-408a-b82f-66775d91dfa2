apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"apiVersion":"flink.apache.org/v1beta1","kind":"FlinkDeployment","metadata":{"annotations":{},"name":"local-flinkdeployment-sample","namespace":"flink"},"spec":{"flinkVersion":"v1_20","image":"flink:1.20","imagePullPolicy":"IfNotPresent","job":{"jarURI":"local:///opt/flink/examples/streaming/StateMachineExample.jar","parallelism":1,"upgradeMode":"stateless"},"jobManager":{"resource":{"cpu":1,"memory":"1024m"}},"mode":"native","podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"local-flinkdeployment-sample"}},"spec":{"containers":[{"image":"bitnami/flink:1.20.2","imagePullPolicy":"IfNotPresent","name":"flink-main-container","volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"flink-conf-temp"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"JOB_TYPE","value":"gdc-job"},{"name":"FLINK_APPLICATION_NAME","value":"local-flink-app"}],"image":"alpine:3.19","name":"flink-init-container","volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/env-flink-custom-configs","name":"env-shared-configs"},{"mountPath":"/tmp/ude/env-gdc-flink-custom-configs","name":"env-gdc-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"devops-tuning"},{"mountPath":"/tmp/ude/flink-conf","name":"flink-conf-temp"}]}],"volumes":[{"configMap":{"defaultMode":365,"items":[{"key":"init-flink.sh","path":"init-flink.sh"}],"name":"ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"env-flink-conf-yaml"},"name":"env-shared-configs"},{"configMap":{"name":"env-gdc-flink-conf-yaml"},"name":"env-gdc-configs"},{"configMap":{"name":"devops-tuning-sample","optional":true},"name":"devops-tuning"},{"emptyDir":{},"name":"flink-conf-temp"}]}},"serviceAccount":"flink","taskManager":{"resource":{"cpu":1,"memory":"1024m"}}}}
  creationTimestamp: "2025-09-25T21:07:39Z"
  finalizers:
  - flinkdeployments.flink.apache.org/finalizer
  generation: 2
  name: local-flinkdeployment-sample
  namespace: flink
  resourceVersion: "2388035"
  uid: 3c0ec302-08ef-424c-8120-77e128f1c1cf
spec:
  flinkVersion: v1_20
  image: flink:1.20
  imagePullPolicy: IfNotPresent
  job:
    args: []
    jarURI: local:///opt/flink/examples/streaming/StateMachineExample.jar
    parallelism: 1
    state: running
    upgradeMode: stateless
  jobManager:
    replicas: 1
    resource:
      cpu: 1
      memory: 1024m
  mode: native
  podTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: local-flinkdeployment-sample
    spec:
      containers:
      - image: bitnami/flink:1.20.2
        imagePullPolicy: IfNotPresent
        name: flink-main-container
        volumeMounts:
        - mountPath: /opt/flink/conf-ude
          name: flink-conf-temp
      initContainers:
      - command:
        - /bin/sh
        - /opt/ude/scripts/init-flink.sh
        env:
        - name: JOB_TYPE
          value: gdc-job
        - name: FLINK_APPLICATION_NAME
          value: local-flink-app
        image: alpine:3.19
        name: flink-init-container
        volumeMounts:
        - mountPath: /opt/ude/scripts
          name: ude-shell-scripts
        - mountPath: /tmp/ude/env-flink-custom-configs
          name: env-shared-configs
        - mountPath: /tmp/ude/env-gdc-flink-custom-configs
          name: env-gdc-configs
        - mountPath: /tmp/ude/flink-devops-tuning
          name: devops-tuning
        - mountPath: /tmp/ude/flink-conf
          name: flink-conf-temp
      volumes:
      - configMap:
          defaultMode: 365
          items:
          - key: init-flink.sh
            path: init-flink.sh
          name: ude-shell-scripts
        name: ude-shell-scripts
      - configMap:
          name: env-flink-conf-yaml
        name: env-shared-configs
      - configMap:
          name: env-gdc-flink-conf-yaml
        name: env-gdc-configs
      - configMap:
          name: devops-tuning-sample
          optional: true
        name: devops-tuning
      - emptyDir: {}
        name: flink-conf-temp
  serviceAccount: flink
  taskManager:
    resource:
      cpu: 1
      memory: 1024m
status:
  clusterInfo:
    flink-revision: 1641cb9 @ 2025-06-12T21:40:37+02:00
    flink-version: 1.20.2
    state-size: "8657"
    total-cpu: "2.0"
    total-memory: "**********"
  jobManagerDeploymentStatus: READY
  jobStatus:
    checkpointInfo:
      lastPeriodicCheckpointTimestamp: 0
    jobId: 375c7cbb7c1cf81024dc586e7a457a7f
    jobName: State machine job
    savepointInfo:
      lastPeriodicSavepointTimestamp: 0
      savepointHistory: []
    startTime: "*************"
    state: RUNNING
    updateTime: "*************"
  lifecycleState: STABLE
  observedGeneration: 2
  reconciliationStatus:
    lastReconciledSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/examples/streaming/StateMachineExample.jar","parallelism":1,"entryClass":null,"args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":null,"image":"flink:1.20","imagePullPolicy":"IfNotPresent","serviceAccount":"flink","flinkVersion":"v1_20","ingress":null,"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"local-flinkdeployment-sample"}},"spec":{"containers":[{"image":"bitnami/flink:1.20.2","imagePullPolicy":"IfNotPresent","name":"flink-main-container","volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"flink-conf-temp"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"JOB_TYPE","value":"gdc-job"},{"name":"FLINK_APPLICATION_NAME","value":"local-flink-app"}],"image":"alpine:3.19","name":"flink-init-container","volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/env-flink-custom-configs","name":"env-shared-configs"},{"mountPath":"/tmp/ude/env-gdc-flink-custom-configs","name":"env-gdc-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"devops-tuning"},{"mountPath":"/tmp/ude/flink-conf","name":"flink-conf-temp"}]}],"volumes":[{"configMap":{"defaultMode":365,"items":[{"key":"init-flink.sh","path":"init-flink.sh"}],"name":"ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"env-flink-conf-yaml"},"name":"env-shared-configs"},{"configMap":{"name":"env-gdc-flink-conf-yaml"},"name":"env-gdc-configs"},{"configMap":{"name":"devops-tuning-sample","optional":true},"name":"devops-tuning"},{"emptyDir":{},"name":"flink-conf-temp"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"1024m","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":1.0,"memory":"1024m","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":null,"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":false}}'
    lastStableSpec: '{"spec":{"job":{"jarURI":"local:///opt/flink/examples/streaming/StateMachineExample.jar","parallelism":1,"entryClass":null,"args":[],"state":"running","savepointTriggerNonce":null,"initialSavepointPath":null,"checkpointTriggerNonce":null,"upgradeMode":"stateless","allowNonRestoredState":null,"savepointRedeployNonce":null,"autoscalerResetNonce":null},"restartNonce":null,"flinkConfiguration":null,"image":"flink:1.20","imagePullPolicy":"IfNotPresent","serviceAccount":"flink","flinkVersion":"v1_20","ingress":null,"podTemplate":{"metadata":{"labels":{"app.kubernetes.io/name":"local-flinkdeployment-sample"}},"spec":{"containers":[{"image":"bitnami/flink:1.20.2","imagePullPolicy":"IfNotPresent","name":"flink-main-container","volumeMounts":[{"mountPath":"/opt/flink/conf-ude","name":"flink-conf-temp"}]}],"initContainers":[{"command":["/bin/sh","/opt/ude/scripts/init-flink.sh"],"env":[{"name":"JOB_TYPE","value":"gdc-job"},{"name":"FLINK_APPLICATION_NAME","value":"local-flink-app"}],"image":"alpine:3.19","name":"flink-init-container","volumeMounts":[{"mountPath":"/opt/ude/scripts","name":"ude-shell-scripts"},{"mountPath":"/tmp/ude/env-flink-custom-configs","name":"env-shared-configs"},{"mountPath":"/tmp/ude/env-gdc-flink-custom-configs","name":"env-gdc-configs"},{"mountPath":"/tmp/ude/flink-devops-tuning","name":"devops-tuning"},{"mountPath":"/tmp/ude/flink-conf","name":"flink-conf-temp"}]}],"volumes":[{"configMap":{"defaultMode":365,"items":[{"key":"init-flink.sh","path":"init-flink.sh"}],"name":"ude-shell-scripts"},"name":"ude-shell-scripts"},{"configMap":{"name":"env-flink-conf-yaml"},"name":"env-shared-configs"},{"configMap":{"name":"env-gdc-flink-conf-yaml"},"name":"env-gdc-configs"},{"configMap":{"name":"devops-tuning-sample","optional":true},"name":"devops-tuning"},{"emptyDir":{},"name":"flink-conf-temp"}]}},"jobManager":{"resource":{"cpu":1.0,"memory":"1024m","ephemeralStorage":null},"replicas":1,"podTemplate":null},"taskManager":{"resource":{"cpu":1.0,"memory":"1024m","ephemeralStorage":null},"replicas":null,"podTemplate":null},"logConfiguration":null,"mode":"native"},"resource_metadata":{"apiVersion":"flink.apache.org/v1beta1","firstDeployment":false}}'
    reconciliationTimestamp: 1758834922443
    state: DEPLOYED
  taskManager:
    labelSelector: component=taskmanager,app=local-flinkdeployment-sample
    replicas: 1
