# Simple Flink Kafka Streaming Job
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: simple-kafka-streaming
  namespace: flink
spec:
  image: flink:1.20-scala_2.12
  flinkVersion: v1_20
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    state.backend: filesystem
    execution.checkpointing.dir: file:///flink-data/checkpoints
    execution.checkpointing.savepoint-dir: file:///flink-data/savepoints
    execution.checkpointing.interval: 60s
  serviceAccount: flink
  jobManager:
    resource:
      memory: "1024m"
      cpu: 1
  taskManager:
    resource:
      memory: "1024m"
      cpu: 1
  podTemplate:
    spec:
      containers:
        - name: flink-main-container
          volumeMounts:
            - mountPath: /flink-data
              name: flink-volume
          env:
            - name: KAFKA_BOOTSTRAP_SERVERS
              value: "test-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092"
      volumes:
        - name: flink-volume
          emptyDir: {}
  job:
    jarURI: local:///opt/flink/examples/streaming/WordCount.jar
    parallelism: 2
    upgradeMode: savepoint
    args:
      - "--input"
      - "/tmp/input.txt"